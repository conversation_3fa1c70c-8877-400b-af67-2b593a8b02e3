#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据库连接和数据
"""

import duckdb

def test_database():
    try:
        conn = duckdb.connect('shuju.duckdb')
        
        # 检查表是否存在
        tables = conn.execute("SELECT table_name FROM information_schema.tables WHERE table_name='PLCFHR_HouseholdInfo'").fetchone()
        print(f"表是否存在: {tables}")
        
        if tables:
            # 检查总记录数
            count = conn.execute('SELECT COUNT(*) FROM PLCFHR_HouseholdInfo').fetchone()[0]
            print(f"总记录数: {count:,}")
            
            # 检查列名
            columns = conn.execute("SELECT column_name FROM information_schema.columns WHERE table_name='PLCFHR_HouseholdInfo' ORDER BY ordinal_position").fetchall()
            print(f"列数: {len(columns)}")
            print("前10个列名:", [col[0] for col in columns[:10]])
            
            # 检查一些样例数据
            sample = conn.execute('SELECT * FROM PLCFHR_HouseholdInfo LIMIT 3').fetchdf()
            print(f"样例数据形状: {sample.shape}")
            
            # 检查关键字段的数据分布
            print("\n关键字段数据分布:")
            
            # 检查hh_type字段
            hh_types = conn.execute('SELECT hh_type, COUNT(*) as count FROM PLCFHR_HouseholdInfo GROUP BY hh_type ORDER BY count DESC LIMIT 5').fetchall()
            print("hh_type分布:", hh_types)
            
            # 检查target_type字段
            target_types = conn.execute('SELECT target_type, COUNT(*) as count FROM PLCFHR_HouseholdInfo WHERE target_type IS NOT NULL GROUP BY target_type ORDER BY count DESC LIMIT 5').fetchall()
            print("target_type分布:", target_types)
            
            # 检查省份分布
            provs = conn.execute('SELECT prov, COUNT(*) as count FROM PLCFHR_HouseholdInfo WHERE prov IS NOT NULL GROUP BY prov ORDER BY count DESC LIMIT 5').fetchall()
            print("省份分布:", provs)
            
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database()
