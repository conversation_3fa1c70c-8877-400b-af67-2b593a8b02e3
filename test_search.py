#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试搜索功能
"""

import duckdb

def test_search_logic():
    """测试搜索逻辑"""
    try:
        conn = duckdb.connect('shuju.duckdb')
        
        print("测试搜索逻辑:")
        
        # 模拟不同的查询条件组合
        test_cases = [
            {
                "name": "无条件查询",
                "conditions": [],
                "params": []
            },
            {
                "name": "只选择脱贫户",
                "conditions": ["hh_type = ?"],
                "params": ["是"]
            },
            {
                "name": "只选择非脱贫户", 
                "conditions": ["hh_type = ?"],
                "params": ["否"]
            },
            {
                "name": "对象类型为脱贫户",
                "conditions": ["target_type = ?"],
                "params": ["脱贫户"]
            },
            {
                "name": "对象类型为一般农户",
                "conditions": ["target_type = ?"],
                "params": ["一般农户"]
            },
            {
                "name": "姓名包含张",
                "conditions": ["name LIKE ?"],
                "params": ["%张%"]
            },
            {
                "name": "组合条件：脱贫户+对象类型脱贫户",
                "conditions": ["hh_type = ?", "target_type = ?"],
                "params": ["是", "脱贫户"]
            },
            {
                "name": "省份为陕西省",
                "conditions": ["prov = ?"],
                "params": ["陕西省"]
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['name']}:")
            
            # 构建查询
            base_query = "SELECT * FROM PLCFHR_HouseholdInfo"
            where_clause = f" WHERE {' AND '.join(test_case['conditions'])}" if test_case['conditions'] else ""
            
            # 计数查询
            count_query = f"SELECT COUNT(*) FROM PLCFHR_HouseholdInfo{where_clause}"
            print(f"   查询语句: {count_query}")
            print(f"   参数: {test_case['params']}")
            
            if test_case['params']:
                count = conn.execute(count_query, test_case['params']).fetchone()[0]
            else:
                count = conn.execute(count_query).fetchone()[0]
            
            print(f"   结果: {count:,} 条记录")
            
            # 如果有结果，测试分页查询
            if count > 0:
                page_query = f"{base_query}{where_clause} ORDER BY hh_id LIMIT 20 OFFSET 0"
                if test_case['params']:
                    result = conn.execute(page_query, test_case['params']).fetchdf()
                else:
                    result = conn.execute(page_query).fetchdf()
                print(f"   分页查询返回: {len(result)} 行")
            else:
                print("   ⚠️ 没有匹配的记录")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_search_logic()
