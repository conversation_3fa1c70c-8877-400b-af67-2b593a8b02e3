#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试查询逻辑
"""

import duckdb

def test_queries():
    try:
        conn = duckdb.connect('shuju.duckdb')
        
        print("测试各种查询条件:")
        
        # 测试1: 无条件查询
        print("\n1. 无条件查询:")
        count1 = conn.execute('SELECT COUNT(*) FROM PLCFHR_HouseholdInfo').fetchone()[0]
        print(f"   总记录数: {count1:,}")
        
        # 测试2: hh_type = '是'
        print("\n2. hh_type = '是':")
        count2 = conn.execute("SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE hh_type = ?", ['是']).fetchone()[0]
        print(f"   脱贫户记录数: {count2:,}")
        
        # 测试3: hh_type = '否'
        print("\n3. hh_type = '否':")
        count3 = conn.execute("SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE hh_type = ?", ['否']).fetchone()[0]
        print(f"   非脱贫户记录数: {count3:,}")
        
        # 测试4: target_type = '脱贫户'
        print("\n4. target_type = '脱贫户':")
        count4 = conn.execute("SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE target_type = ?", ['脱贫户']).fetchone()[0]
        print(f"   对象类型为脱贫户的记录数: {count4:,}")
        
        # 测试5: 姓名模糊查询
        print("\n5. 姓名包含'张':")
        count5 = conn.execute("SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE name LIKE ?", ['%张%']).fetchone()[0]
        print(f"   姓名包含'张'的记录数: {count5:,}")
        
        # 测试6: 组合条件
        print("\n6. 组合条件 (hh_type='是' AND target_type='脱贫户'):")
        count6 = conn.execute("SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE hh_type = ? AND target_type = ?", ['是', '脱贫户']).fetchone()[0]
        print(f"   组合条件记录数: {count6:,}")
        
        # 测试7: 分页查询
        print("\n7. 分页查询 (前20条):")
        result7 = conn.execute("SELECT * FROM PLCFHR_HouseholdInfo ORDER BY hh_id LIMIT 20 OFFSET 0").fetchdf()
        print(f"   查询结果行数: {len(result7)}")
        print(f"   查询结果列数: {len(result7.columns)}")
        
        # 测试8: 检查字段是否存在
        print("\n8. 检查关键字段:")
        columns = conn.execute("SELECT column_name FROM information_schema.columns WHERE table_name='PLCFHR_HouseholdInfo'").fetchall()
        key_fields = ['name', 'id_no', 'target_type', 'hh_type', 'prov', 'city', 'county', 'town', 'vill']
        for field in key_fields:
            exists = any(col[0] == field for col in columns)
            print(f"   {field}: {'✅' if exists else '❌'}")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_queries()
