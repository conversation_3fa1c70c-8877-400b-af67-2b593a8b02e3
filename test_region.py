#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试区域查询逻辑
"""

import duckdb

def test_region_queries():
    try:
        conn = duckdb.connect('shuju.duckdb')
        
        print("测试区域查询逻辑:")
        
        # 获取实际的区域数据
        print("\n1. 获取实际区域数据:")
        prov_data = conn.execute('SELECT DISTINCT prov FROM PLCFHR_HouseholdInfo WHERE prov IS NOT NULL').fetchall()
        print(f"   省份: {[row[0] for row in prov_data]}")
        
        city_data = conn.execute('SELECT DISTINCT city FROM PLCFHR_HouseholdInfo WHERE city IS NOT NULL').fetchall()
        print(f"   市: {[row[0] for row in city_data]}")
        
        county_data = conn.execute('SELECT DISTINCT county FROM PLCFHR_HouseholdInfo WHERE county IS NOT NULL').fetchall()
        print(f"   县: {[row[0] for row in county_data]}")
        
        town_data = conn.execute('SELECT DISTINCT town FROM PLCFHR_HouseholdInfo WHERE town IS NOT NULL LIMIT 10').fetchall()
        print(f"   镇(前10个): {[row[0] for row in town_data]}")
        
        # 测试不同级别的查询
        print("\n2. 测试不同级别的查询:")
        
        # 只选择省份
        if prov_data:
            prov = prov_data[0][0]
            count1 = conn.execute('SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE prov = ?', [prov]).fetchone()[0]
            print(f"   只选省份 '{prov}': {count1:,} 条记录")
        
        # 只选择市
        if city_data:
            city = city_data[0][0]
            count2 = conn.execute('SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE city = ?', [city]).fetchone()[0]
            print(f"   只选市 '{city}': {count2:,} 条记录")
        
        # 只选择县
        if county_data:
            county = county_data[0][0]
            count3 = conn.execute('SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE county = ?', [county]).fetchone()[0]
            print(f"   只选县 '{county}': {count3:,} 条记录")
        
        # 只选择镇
        if town_data:
            town = town_data[0][0]
            count4 = conn.execute('SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE town = ?', [town]).fetchone()[0]
            print(f"   只选镇 '{town}': {count4:,} 条记录")
        
        # 组合查询
        print("\n3. 测试组合查询:")
        if prov_data and city_data:
            prov = prov_data[0][0]
            city = city_data[0][0]
            count5 = conn.execute('SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE prov = ? AND city = ?', [prov, city]).fetchone()[0]
            print(f"   省+市 '{prov}' + '{city}': {count5:,} 条记录")
        
        if prov_data and city_data and county_data:
            prov = prov_data[0][0]
            city = city_data[0][0]
            county = county_data[0][0]
            count6 = conn.execute('SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE prov = ? AND city = ? AND county = ?', [prov, city, county]).fetchone()[0]
            print(f"   省+市+县 '{prov}' + '{city}' + '{county}': {count6:,} 条记录")
        
        # 检查数据完整性
        print("\n4. 检查数据完整性:")
        null_counts = {}
        for field in ['prov', 'city', 'county', 'town', 'vill']:
            null_count = conn.execute(f'SELECT COUNT(*) FROM PLCFHR_HouseholdInfo WHERE {field} IS NULL OR {field} = ""').fetchone()[0]
            total_count = conn.execute('SELECT COUNT(*) FROM PLCFHR_HouseholdInfo').fetchone()[0]
            null_counts[field] = null_count
            print(f"   {field} 为空的记录: {null_count:,} / {total_count:,} ({null_count/total_count*100:.1f}%)")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_region_queries()
