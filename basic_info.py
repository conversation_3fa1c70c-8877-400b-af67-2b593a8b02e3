#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础信息页面模块 - 户信息表
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableView, QPushButton, QLineEdit, QComboBox,
                            QGroupBox, QGridLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QSplitter, QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt, QAbstractTableModel, QVariant, QTimer
from PyQt5.QtGui import QFont, QStandardItemModel, QStandardItem
import pandas as pd


class HouseholdTableModel(QAbstractTableModel):
    """户信息表格模型"""

    def __init__(self, data=None):
        super().__init__()
        self._data = data if data is not None else pd.DataFrame()
        self._headers = []
        if not self._data.empty:
            self._headers = list(self._data.columns)

        # 中英文列标题映射
        self.column_mapping = {
            'prov': '省',
            'city': '市',
            'county': '县',
            'town': '镇',
            'vill': '村',
            'grp': '组',
            'hh_id': '户编号',
            'name': '姓名',
            'id_no': '证件号码',
            'id_type': '证件类型',
            'rel_head': '与户主关系',
            'sex': '性别',
            'age': '年龄',
            'dob': '出生日期',
            'phone': '联系电话',
            'target_type': '对象类型',
            'hh_type': '是否脱贫户',
            'exit_yr': '脱贫年份',
            'grid_gorker': '网格员',
            'grid_ghone': '网格员电话',
            'collector': '采集人',
            'collector_chone': '采集人电话',
            'ceviewer': '审核人',
            'ceviewer_chone': '审核人电话',
            'eth': '民族',
            'pol_stat': '政治面貌',
            'health': '健康状态',
            'stu_stat': '在校生状况',
            'no_sch': '失学或辍学原因',
            'edu': '文化程度',
            'skill': '劳动技能',
            'residence': '居住情况',
            'employed_yn': '是否务工',
            'work_addr': '务工地址',
            'work_region': '务工区域',
            'work_rur': '务工时间',
            'emp_chan': '就业渠道',
            'pub_job': '公益岗位类型',
            'pub_mon': '公益岗位聘用时间',
            'Industry': '所属产业',
            'pw_yn': '是否参加公益岗位',
            'med_Ins_yn': '是否参加医疗保险',
            'med_ins_detail': '参加医疗保险情况',
            'pens_Ins_yn': '是否参加养老保险',
            'pens_ins_detail': '参加养老保险情况',
            'urp_yn': '是否城乡居民养老保险或城镇职工养老保险',
            'ss_yn': '是否享受社会保障',
            'ss_detail': '享受社会保障情况',
            'com_ins_yn': '是否参加商业补充医疗保险',
            'crit_aid_yn': '是否接受大病医疗救助',
            'health_support_yn': '是否接受其他健康帮扶',
            'oth_pens_yn': '是否参加其他养老保险',
            'nat_dec_yn': '自然减少状态',
            'nat_dec_reason': '自然减少原因',
            'lon': '经度',
            'lat': '纬度',
            'add_reason': '增加原因',
            'endemic_disease': '地方性疾病类型'
        }

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._data.columns) if not self._data.empty else 0

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or self._data.empty:
            return QVariant()

        if role == Qt.DisplayRole:
            value = self._data.iloc[index.row(), index.column()]
            return str(value) if pd.notna(value) else ""

        return QVariant()

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                if section < len(self._headers):
                    english_header = self._headers[section]
                    # 返回中文标题，如果映射中没有则返回原英文标题
                    return self.column_mapping.get(english_header, english_header)
                return ""
            else:
                return str(section + 1)
        return QVariant()

    def update_data(self, new_data):
        """更新表格数据"""
        print(f"模型更新数据: {len(new_data) if new_data is not None else 0} 条记录")
        self.beginResetModel()
        self._data = new_data if new_data is not None else pd.DataFrame()
        self._headers = list(self._data.columns) if not self._data.empty else []
        print(f"模型数据更新完成: 行数={len(self._data)}, 列数={len(self._headers)}")
        self.endResetModel()
        print("模型重置完成")


class HouseholdInfoPage:
    """户信息页面类"""

    def __init__(self, parent=None):
        self.parent = parent
        self.table_model = HouseholdTableModel()
        self.current_data = pd.DataFrame()

        # 分页相关属性
        self.current_page = 1
        self.page_size = 20  # 每页显示20条记录
        self.total_records = 0
        self.total_pages = 0

    def create_page(self):
        """创建户信息页面"""
        page = QWidget()
        layout = QVBoxLayout(page)

        # 页面标题
        title = QLabel("户信息管理")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # 创建选项卡
        tab_widget = QTabWidget()


        # 户信息查询选项卡
        query_tab = self.create_query_tab()
        tab_widget.addTab(query_tab, "户信息查询")

        # 户信息统计选项卡
        stats_tab = self.create_stats_tab()
        tab_widget.addTab(stats_tab, "户信息统计")

        layout.addWidget(tab_widget)

        # 数据将在create_query_tab中初始化
        print("基础信息页面创建完成，数据将在选项卡中初始化")

        return page

    def create_query_tab(self):
        """创建户信息查询选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 查询条件区域
        query_group = QGroupBox("查询条件")
        query_main_layout = QVBoxLayout()
        query_main_layout.setContentsMargins(10, 10, 10, 10)
        query_main_layout.setSpacing(8)

        # 第一行：查询区域 - 多级联动选择器
        region_row_layout = QHBoxLayout()
        region_row_layout.setSpacing(0)  # 控件之间无间隔
        region_row_layout.setContentsMargins(0, 0, 0, 0)

        # 查询区域标签
        area_label = QLabel("区域:")
        area_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        region_row_layout.addWidget(area_label)

        # 创建区域选择器容器
        self.region_container = QWidget()
        region_layout = QHBoxLayout(self.region_container)
        region_layout.setContentsMargins(0, 0, 0, 0)
        region_layout.setSpacing(5)

        # 省级下拉框
        self.prov_combo = QComboBox()
        self.prov_combo.setFixedWidth(100)
        region_layout.addWidget(self.prov_combo)

        # 市级下拉框
        self.city_combo = QComboBox()
        self.city_combo.setFixedWidth(100)
        self.city_combo.setEnabled(False)
        region_layout.addWidget(self.city_combo)

        # 县级下拉框
        self.county_combo = QComboBox()
        self.county_combo.setFixedWidth(100)
        self.county_combo.setEnabled(False)
        region_layout.addWidget(self.county_combo)

        # 镇级下拉框
        self.town_combo = QComboBox()
        self.town_combo.setFixedWidth(100)
        self.town_combo.setEnabled(False)
        region_layout.addWidget(self.town_combo)

        # 村级下拉框
        self.vill_combo = QComboBox()
        self.vill_combo.setFixedWidth(100)
        self.vill_combo.setEnabled(False)
        region_layout.addWidget(self.vill_combo)

        region_row_layout.addWidget(self.region_container)

        # 添加弹性空间，让控件左对齐
        region_row_layout.addStretch()

        # 第二行：其他查询条件和按钮
        condition_row_layout = QHBoxLayout()
        condition_row_layout.setSpacing(0)  # 控件之间无间隔
        condition_row_layout.setContentsMargins(0, 0, 0, 0)

        # 姓名
        name_label = QLabel("姓名:")
        name_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        condition_row_layout.addWidget(name_label)
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("请输入姓名")
        self.name_input.setFixedWidth(120)
        condition_row_layout.addWidget(self.name_input)

        # 添加条件间隔
        condition_row_layout.addSpacing(20)

        # 证件号码
        id_label = QLabel("证件号码:")
        id_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        condition_row_layout.addWidget(id_label)
        self.id_no_input = QLineEdit()
        self.id_no_input.setPlaceholderText("请输入证件号码")
        self.id_no_input.setFixedWidth(150)
        condition_row_layout.addWidget(self.id_no_input)

        # 添加条件间隔
        condition_row_layout.addSpacing(20)

        # 户类型
        type_label = QLabel("户类型:")
        type_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        condition_row_layout.addWidget(type_label)
        self.target_type_combo = QComboBox()
        self.target_type_combo.addItems(["全部", "脱贫户", "一般户"])
        self.target_type_combo.setFixedWidth(120)
        condition_row_layout.addWidget(self.target_type_combo)

        # 添加条件间隔
        condition_row_layout.addSpacing(20)

        # 是否脱贫户
        hh_label = QLabel("是否脱贫户:")
        hh_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        condition_row_layout.addWidget(hh_label)
        self.hh_type_combo = QComboBox()
        self.hh_type_combo.addItems(["全部", "是", "否"])
        self.hh_type_combo.setFixedWidth(100)
        condition_row_layout.addWidget(self.hh_type_combo)

        # 添加条件间隔
        condition_row_layout.addSpacing(20)

        # 查询和重置按钮
        query_btn = QPushButton("查询")
        query_btn.clicked.connect(self.search_households)
        query_btn.setFixedWidth(80)
        condition_row_layout.addWidget(query_btn)

        reset_btn = QPushButton("重置")
        reset_btn.clicked.connect(self.reset_search)
        reset_btn.setFixedWidth(80)
        condition_row_layout.addWidget(reset_btn)

        # 添加弹性空间，让控件左对齐
        condition_row_layout.addStretch()

        # 将两行添加到主布局
        query_main_layout.addLayout(region_row_layout)
        query_main_layout.addLayout(condition_row_layout)

        query_group.setLayout(query_main_layout)
        layout.addWidget(query_group)

        # 数据表格区域
        table_group = QGroupBox("户信息列表")
        table_layout = QVBoxLayout()

        # 创建表格视图
        self.table_view = QTableView()
        self.table_view.setModel(self.table_model)

        # 设置表格样式 - 网格线和灰色标题
        self.table_view.setStyleSheet("""
            QTableView {
                gridline-color: #d0d0d0;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                padding: 2px 4px;
                height: 20px;
            }
        """)

        # 设置表头高度
        header = self.table_view.horizontalHeader()
        header.setDefaultSectionSize(80)
        header.setMinimumSectionSize(50)
        header.setMaximumHeight(25)

        # 显示网格线
        self.table_view.setShowGrid(True)
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_view.horizontalHeader().setStretchLastSection(True)
        table_layout.addWidget(self.table_view)

        # 分页控件区域
        pagination_layout = QHBoxLayout()

        # 分页信息标签
        self.page_info_label = QLabel("第 1 页，共 1 页 (总计 0 条记录)")
        pagination_layout.addWidget(self.page_info_label)

        pagination_layout.addStretch()

        # 每页显示数量选择
        pagination_layout.addWidget(QLabel("每页显示:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["20","50", "100", "200", "500"])
        self.page_size_combo.setCurrentText("20")
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        pagination_layout.addWidget(self.page_size_combo)

        # 分页按钮 - 简约专业图标
        self.first_page_btn = QPushButton("|< 首页")
        self.first_page_btn.clicked.connect(lambda: self.handle_pagination_click("first"))

        self.prev_page_btn = QPushButton("< 上页")
        self.prev_page_btn.clicked.connect(lambda: self.handle_pagination_click("prev"))

        self.next_page_btn = QPushButton("下页 >")
        self.next_page_btn.clicked.connect(lambda: self.handle_pagination_click("next"))

        self.last_page_btn = QPushButton("末页 >|")
        self.last_page_btn.clicked.connect(lambda: self.handle_pagination_click("last"))





        for btn in [self.first_page_btn, self.prev_page_btn, self.next_page_btn, self.last_page_btn]:
            pagination_layout.addWidget(btn)



        table_layout.addLayout(pagination_layout)

        # 操作按钮区域
        button_layout = QHBoxLayout()

        # 导出按钮
        export_btn = QPushButton("导出数据")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        export_btn.clicked.connect(self.export_data)
        button_layout.addWidget(export_btn)

        button_layout.addStretch()
        table_layout.addLayout(button_layout)

        table_group.setLayout(table_layout)
        layout.addWidget(table_group)

        # 在表格创建完成后初始化数据
        print("🚀 查询选项卡创建完成，开始初始化数据...")
        print(f"🔧 初始化状态: current_page={self.current_page}, page_size={self.page_size}")
        try:
            self.load_target_types()  # 加载对象类型选项
            self.init_region_selectors()  # 初始化区域选择器
            self.load_household_data()
            print(f"🎯 初始化完成后状态: current_page={self.current_page}, total_pages={self.total_pages}, total_records={self.total_records}")
        except Exception as e:
            print(f"❌ 初始化数据时出错: {e}")
            import traceback
            traceback.print_exc()

        return tab

    def create_stats_tab(self):
        """创建户信息统计选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)

        # 统计信息区域
        stats_group = QGroupBox("户信息统计")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        stats_layout = QGridLayout()

        # 统计标签
        self.total_households_label = QLabel("总户数: 加载中...")
        self.total_households_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        stats_layout.addWidget(self.total_households_label, 0, 0)

        self.poverty_households_label = QLabel("脱贫户数: 加载中...")
        self.poverty_households_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #27ae60;")
        stats_layout.addWidget(self.poverty_households_label, 0, 1)

        self.monitor_households_label = QLabel("监测户数: 加载中...")
        self.monitor_households_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #f39c12;")
        stats_layout.addWidget(self.monitor_households_label, 1, 0)

        self.normal_households_label = QLabel("其他户数: 加载中...")
        self.normal_households_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #3498db;")
        stats_layout.addWidget(self.normal_households_label, 1, 1)

        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        # 详细统计表格
        detail_group = QGroupBox("户类型分布统计")
        detail_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        detail_layout = QVBoxLayout()

        # 统计表格
        self.stats_table = QTableView()
        self.stats_model = QStandardItemModel()
        self.stats_table.setModel(self.stats_model)

        # 设置统计表格样式 - 网格线和灰色标题
        self.stats_table.setStyleSheet("""
            QTableView {
                gridline-color: #d0d0d0;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                padding: 2px 4px;
                height: 20px;
            }
        """)

        # 设置统计表格表头高度
        stats_header = self.stats_table.horizontalHeader()
        stats_header.setDefaultSectionSize(80)
        stats_header.setMinimumSectionSize(50)
        stats_header.setMaximumHeight(25)

        # 显示网格线
        self.stats_table.setShowGrid(True)

        self.stats_table.setAlternatingRowColors(True)
        detail_layout.addWidget(self.stats_table)

        # 刷新统计按钮
        refresh_stats_btn = QPushButton("刷新统计")
        refresh_stats_btn.clicked.connect(self.load_statistics)
        detail_layout.addWidget(refresh_stats_btn)

        detail_group.setLayout(detail_layout)
        layout.addWidget(detail_group)

        return tab

    def load_household_data(self, reset_page=True):
        """加载户信息数据（支持分页）"""
        conn = None
        try:
            # 直接连接shuju.duckdb数据库
            import duckdb
            import os

            # 检查数据库文件是否存在
            db_path = 'shuju.duckdb'
            if not os.path.exists(db_path):
                raise FileNotFoundError(f"数据库文件不存在: {db_path}")

            conn = duckdb.connect(db_path)

            # 如果重置页面，回到第一页
            if reset_page:
                self.current_page = 1

            # 首先查询总记录数
            count_query = "SELECT COUNT(*) FROM PLCFHR_HouseholdInfo"

            count_result = conn.execute(count_query).fetchone()
            self.total_records = count_result[0] if count_result else 0

            # 确保总页数计算正确
            if self.total_records > 0:
                self.total_pages = (self.total_records + self.page_size - 1) // self.page_size
            else:
                self.total_pages = 1



            # 如果没有数据，显示空表格
            if self.total_records == 0:
                self.current_data = pd.DataFrame()
                self.table_model.update_data(pd.DataFrame())
                self.update_pagination_info()
                if hasattr(self, 'page_info_label'):
                    self.page_info_label.setText("没有数据")

                return

            # 确保当前页在有效范围内
            if self.current_page > self.total_pages:
                self.current_page = self.total_pages
            elif self.current_page < 1:
                self.current_page = 1

            # 计算OFFSET
            offset = (self.current_page - 1) * self.page_size

            # 分页查询数据
            query = f"""
            SELECT * FROM PLCFHR_HouseholdInfo
            ORDER BY hh_id
            LIMIT {self.page_size} OFFSET {offset}
            """

            result = conn.execute(query).fetchdf()

            # 更新数据和视图
            self.current_data = result
            self.table_model.update_data(result)

            # 调整表格列宽和刷新视图
            if hasattr(self, 'table_view') and self.table_view:
                self.table_view.resizeColumnsToContents()
                self.table_view.viewport().update()
                self.table_view.update()

            # 更新分页信息
            self.update_pagination_info()



        except Exception as e:
            error_message = f"加载户信息数据时出错: {str(e)}"
            QMessageBox.warning(None, "错误", error_message)
            if hasattr(self, 'page_info_label'):
                self.page_info_label.setText("数据加载失败")
            # 设置默认值以防止后续错误
            self.total_records = 0
            self.total_pages = 1
            self.current_data = pd.DataFrame()
            if hasattr(self, 'table_model'):
                self.table_model.update_data(pd.DataFrame())
        finally:
            # 确保在任何情况下都关闭连接
            if conn is not None:
                try:
                    conn.close()
                except:
                    pass

    def update_pagination_info(self):
        """更新分页信息"""
        # 更新分页信息标签
        if hasattr(self, 'page_info_label'):
            if self.total_records > 0:
                start_record = (self.current_page - 1) * self.page_size + 1
                end_record = min(self.current_page * self.page_size, self.total_records)
                info_text = f"第 {self.current_page} 页，共 {self.total_pages} 页 (显示 {start_record}-{end_record}，总计 {self.total_records:,} 条记录)"
            else:
                info_text = "没有数据"
            self.page_info_label.setText(info_text)
            print(f"📄 分页信息更新: {info_text}")

        # 更新分页按钮状态
        if hasattr(self, 'first_page_btn') and hasattr(self, 'prev_page_btn') and \
           hasattr(self, 'next_page_btn') and hasattr(self, 'last_page_btn'):

            # 计算按钮启用状态
            first_enabled = self.current_page > 1 and self.total_pages > 1
            prev_enabled = self.current_page > 1 and self.total_pages > 1
            next_enabled = self.current_page < self.total_pages and self.total_pages > 1
            last_enabled = self.current_page < self.total_pages and self.total_pages > 1

            print(f"🔘 分页按钮状态计算:")
            print(f"   当前页={self.current_page}, 总页数={self.total_pages}, 总记录数={self.total_records}")
            print(f"   首页按钮启用: {first_enabled}, 上页按钮启用: {prev_enabled}")
            print(f"   下页按钮启用: {next_enabled}, 末页按钮启用: {last_enabled}")

            # 设置按钮状态
            self.first_page_btn.setEnabled(first_enabled)
            self.prev_page_btn.setEnabled(prev_enabled)
            self.next_page_btn.setEnabled(next_enabled)
            self.last_page_btn.setEnabled(last_enabled)

            # 强制刷新按钮样式和状态
            for btn in [self.first_page_btn, self.prev_page_btn, self.next_page_btn, self.last_page_btn]:
                btn.update()
                btn.repaint()
                print(f"   按钮 '{btn.text()}' 状态: 启用={btn.isEnabled()}")
        else:
            print("⚠️ 警告: 分页按钮对象不存在")

    def change_page_size(self, new_size):
        """改变每页显示数量"""
        try:
            self.page_size = int(new_size)
            print(f"每页显示数量改为: {self.page_size}")
            self.load_household_data(reset_page=True)
        except ValueError:
            print(f"无效的页面大小: {new_size}")

    def go_first_page(self):
        """跳转到首页"""
        print(f"🔘 点击首页按钮: 当前页={self.current_page}, 总页数={self.total_pages}")
        try:
            if self.total_pages > 1 and self.current_page > 1:
                old_page = self.current_page
                self.current_page = 1
                print(f"📄 跳转到首页: {old_page} -> {self.current_page}")
                self.load_household_data(reset_page=False)
            else:
                print("ℹ️ 已经在首页或只有一页，无需跳转")
        except Exception as e:
            print(f"❌ 跳转首页时出错: {e}")
            import traceback
            traceback.print_exc()

    def go_prev_page(self):
        """跳转到上一页"""
        print(f"🔘 点击上页按钮: 当前页={self.current_page}, 总页数={self.total_pages}")
        try:
            if self.total_pages > 1 and self.current_page > 1:
                old_page = self.current_page
                self.current_page -= 1
                print(f"📄 跳转到上一页: {old_page} -> {self.current_page}")
                self.load_household_data(reset_page=False)
            else:
                print("ℹ️ 已经在首页或只有一页，无法跳转到上一页")
        except Exception as e:
            print(f"❌ 跳转上一页时出错: {e}")
            import traceback
            traceback.print_exc()

    def go_next_page(self):
        """跳转到下一页"""
        print(f"🔘 点击下页按钮: 当前页={self.current_page}, 总页数={self.total_pages}")
        try:
            if self.total_pages > 1 and self.current_page < self.total_pages:
                old_page = self.current_page
                self.current_page += 1
                print(f"📄 跳转到下一页: {old_page} -> {self.current_page}")
                self.load_household_data(reset_page=False)
            else:
                print("ℹ️ 已经在末页或只有一页，无法跳转到下一页")
        except Exception as e:
            print(f"❌ 跳转下一页时出错: {e}")
            import traceback
            traceback.print_exc()

    def go_last_page(self):
        """跳转到末页"""
        print(f"🔘 点击末页按钮: 当前页={self.current_page}, 总页数={self.total_pages}")
        try:
            if self.total_pages > 1 and self.current_page < self.total_pages:
                old_page = self.current_page
                self.current_page = self.total_pages
                print(f"📄 跳转到末页: {old_page} -> {self.current_page}")
                self.load_household_data(reset_page=False)
            else:
                print("ℹ️ 已经在末页或只有一页，无需跳转")
        except Exception as e:
            print(f"❌ 跳转末页时出错: {e}")
            import traceback
            traceback.print_exc()



    def handle_pagination_click(self, direction):
        """统一处理分页按钮点击"""
        try:
            # 执行相应的分页操作
            if direction == "first":
                self.go_first_page()
            elif direction == "prev":
                self.go_prev_page()
            elif direction == "next":
                self.go_next_page()
            elif direction == "last":
                self.go_last_page()

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(None, "错误", f"处理分页点击时出错: {str(e)}")





    def search_households(self):
        """搜索户信息（支持分页）"""
        try:
            print("=" * 50)
            print("🔍 查询按钮被点击！开始执行查询...")
            print("=" * 50)

            # 直接连接shuju.duckdb数据库
            import duckdb
            conn = duckdb.connect('shuju.duckdb')

            # 构建查询条件
            conditions = []
            params = []

            # 姓名条件
            if self.name_input.text().strip():
                conditions.append("name LIKE ?")
                params.append(f"%{self.name_input.text().strip()}%")
                print(f"📝 添加姓名条件: {self.name_input.text().strip()}")

            # 证件号码条件
            if self.id_no_input.text().strip():
                conditions.append("id_no LIKE ?")
                params.append(f"%{self.id_no_input.text().strip()}%")
                print(f"🆔 添加证件号码条件: {self.id_no_input.text().strip()}")

            # 对象类型条件
            if self.target_type_combo.currentText() != "全部":
                conditions.append("target_type = ?")
                params.append(self.target_type_combo.currentText())
                print(f"🏷️ 添加对象类型条件: {self.target_type_combo.currentText()}")

            # 是否脱贫户条件
            if self.hh_type_combo.currentText() != "全部":
                if self.hh_type_combo.currentText() == "是":
                    conditions.append("hh_type = ?")
                    params.append("是")
                else:  # "否"
                    conditions.append("hh_type = ?")
                    params.append("否")
                print(f"🏠 添加脱贫户条件: {self.hh_type_combo.currentText()}")

            # 查询区域条件 - 多级联动
            if hasattr(self, 'prov_combo') and self.prov_combo.currentText() != "全部省份":
                conditions.append("prov = ?")
                params.append(self.prov_combo.currentText())
                print(f"🌍 添加省份条件: {self.prov_combo.currentText()}")

            if hasattr(self, 'city_combo') and self.city_combo.currentText() != "全部市":
                conditions.append("city = ?")
                params.append(self.city_combo.currentText())
                print(f"🏙️ 添加城市条件: {self.city_combo.currentText()}")

            if hasattr(self, 'county_combo') and self.county_combo.currentText() != "全部县":
                conditions.append("county = ?")
                params.append(self.county_combo.currentText())
                print(f"🏘️ 添加县区条件: {self.county_combo.currentText()}")

            if hasattr(self, 'town_combo') and self.town_combo.currentText() != "全部镇":
                conditions.append("town = ?")
                params.append(self.town_combo.currentText())
                print(f"🏡 添加乡镇条件: {self.town_combo.currentText()}")

            if hasattr(self, 'vill_combo') and self.vill_combo.currentText() != "全部村":
                conditions.append("vill = ?")
                params.append(self.vill_combo.currentText())
                print(f"🏠 添加村庄条件: {self.vill_combo.currentText()}")

            print(f"📋 查询条件总数: {len(conditions)}")
            print(f"📋 查询参数: {params}")

            # 重置到第一页
            self.current_page = 1

            # 构建基础查询
            base_query = "SELECT * FROM PLCFHR_HouseholdInfo"
            where_clause = f" WHERE {' AND '.join(conditions)}" if conditions else ""

            # 先查询总记录数
            count_query = f"SELECT COUNT(*) FROM PLCFHR_HouseholdInfo{where_clause}"
            print(f"📊 计数查询: {count_query}")
            print(f"📊 查询参数: {params}")

            if params:
                self.total_records = conn.execute(count_query, params).fetchone()[0]
            else:
                self.total_records = conn.execute(count_query).fetchone()[0]

            print(f"📈 查询到总记录数: {self.total_records}")
            self.total_pages = (self.total_records + self.page_size - 1) // self.page_size

            # 分页查询数据
            offset = (self.current_page - 1) * self.page_size
            query = f"{base_query}{where_clause} ORDER BY hh_id LIMIT {self.page_size} OFFSET {offset}"
            print(f"📄 分页查询: {query}")

            # 执行查询
            if params:
                result = conn.execute(query, params).fetchdf()
            else:
                result = conn.execute(query).fetchdf()

            print(f"📊 查询结果行数: {len(result)}")

            # 更新表格数据
            self.current_data = result
            self.table_model.update_data(result)

            if hasattr(self, 'table_view') and self.table_view:
                self.table_view.resizeColumnsToContents()

            # 更新分页信息
            self.update_pagination_info()

            QMessageBox.information(None, "查询完成", f"找到 {self.total_records} 条匹配记录，当前显示第 {self.current_page} 页")

            conn.close()

        except Exception as e:
            print(f"❌ 搜索户信息时出错: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(None, "错误", f"搜索户信息时出错: {str(e)}")

    def reset_search(self):
        """重置查询条件"""
        self.name_input.clear()
        self.id_no_input.clear()
        self.target_type_combo.setCurrentIndex(0)
        self.hh_type_combo.setCurrentIndex(0)

        # 重置区域选择器
        if hasattr(self, 'prov_combo'):
            self.prov_combo.setCurrentIndex(0)
        if hasattr(self, 'city_combo'):
            self.city_combo.clear()
            self.city_combo.setEnabled(False)
        if hasattr(self, 'county_combo'):
            self.county_combo.clear()
            self.county_combo.setEnabled(False)
        if hasattr(self, 'town_combo'):
            self.town_combo.clear()
            self.town_combo.setEnabled(False)
        if hasattr(self, 'vill_combo'):
            self.vill_combo.clear()
            self.vill_combo.setEnabled(False)

        # 重新加载所有数据（重置到第一页）
        self.load_household_data(reset_page=True)

    def load_target_types(self):
        """从数据库加载对象类型选项"""
        try:
            import duckdb
            conn = duckdb.connect('shuju.duckdb')

            # 查询所有不同的对象类型
            query = """
            SELECT DISTINCT target_type, COUNT(*) as count
            FROM PLCFHR_HouseholdInfo
            WHERE target_type IS NOT NULL AND target_type != ''
            GROUP BY target_type
            ORDER BY count DESC, target_type
            """

            result = conn.execute(query).fetchall()

            # 清空现有选项（保留"全部"）
            current_text = self.target_type_combo.currentText()
            self.target_type_combo.clear()
            self.target_type_combo.addItem("全部")

            # 添加从数据库获取的选项
            for row in result:
                if row[0]:  # 确保不是空值
                    self.target_type_combo.addItem(row[0])

            # 恢复之前的选择
            index = self.target_type_combo.findText(current_text)
            if index >= 0:
                self.target_type_combo.setCurrentIndex(index)

            conn.close()
            print(f"✅ 已加载 {len(result)} 个对象类型选项")

        except Exception as e:
            print(f"❌ 加载对象类型时出错: {e}")
            # 如果出错，使用从查询结果得到的默认选项
            self.target_type_combo.clear()
            self.target_type_combo.addItems(["全部", "一般农户", "脱贫户", "突发严重困难户", "边缘易致贫户", "脱贫不稳定户"])

    def load_statistics(self):
        """加载统计信息"""
        try:
            # 直接连接shuju.duckdb数据库
            import duckdb
            conn = duckdb.connect('shuju.duckdb')

            # 查询总户数
            total_query = "SELECT COUNT(*) as total FROM PLCFHR_HouseholdInfo"
            total_result = conn.execute(total_query).fetchone()
            total_count = total_result[0] if total_result else 0

            # 查询各户类型分布
            type_query = """
            SELECT hh_type, COUNT(*) as count
            FROM PLCFHR_HouseholdInfo
            WHERE hh_type IS NOT NULL AND hh_type != ''
            GROUP BY hh_type
            ORDER BY count DESC
            """
            type_result = conn.execute(type_query).fetchall()

            # 查询各省份户数分布
            prov_query = """
            SELECT prov, COUNT(*) as count
            FROM PLCFHR_HouseholdInfo
            GROUP BY prov
            ORDER BY count DESC
            """
            prov_result = conn.execute(prov_query).fetchall()

            # 更新统计标签
            self.total_households_label.setText(f"总户数: {total_count:,}")

            # 统计各类型户数
            poverty_count = 0
            monitor_count = 0
            normal_count = 0

            for row in type_result:
                hh_type = row[0] if row[0] else ""
                count = row[1]

                if "脱贫" in hh_type or "已脱贫" in hh_type:
                    poverty_count += count
                elif "监测" in hh_type or "边缘" in hh_type:
                    monitor_count += count
                else:
                    normal_count += count

            self.poverty_households_label.setText(f"脱贫户数: {poverty_count:,}")
            self.monitor_households_label.setText(f"监测户数: {monitor_count:,}")
            self.normal_households_label.setText(f"其他户数: {normal_count:,}")

            # 更新详细统计表格
            self.update_stats_table(type_result)

            conn.close()

        except Exception as e:
            print(f"加载统计信息时出错: {e}")

    def update_stats_table(self, data):
        """更新统计表格"""
        try:
            self.stats_model.clear()
            self.stats_model.setHorizontalHeaderLabels(['户类型', '户数', '占比'])

            total_count = sum(row[1] for row in data) if data else 0

            for row in data:
                hh_type = row[0] if row[0] else "未知类型"
                count = row[1]
                percentage = f"{(count/total_count*100):.1f}%" if total_count > 0 else "0%"

                type_item = QStandardItem(hh_type)
                count_item = QStandardItem(f"{count:,}")
                percentage_item = QStandardItem(percentage)

                self.stats_model.appendRow([type_item, count_item, percentage_item])

            # 调整列宽
            self.stats_table.resizeColumnsToContents()

        except Exception as e:
            print(f"更新统计表格时出错: {e}")

    def get_current_query_conditions(self):
        """获取当前的查询条件和参数"""
        conditions = []
        params = []

        # 姓名条件
        if hasattr(self, 'name_input') and self.name_input.text().strip():
            conditions.append("name LIKE ?")
            params.append(f"%{self.name_input.text().strip()}%")

        # 证件号码条件
        if hasattr(self, 'id_no_input') and self.id_no_input.text().strip():
            conditions.append("id_no LIKE ?")
            params.append(f"%{self.id_no_input.text().strip()}%")

        # 对象类型条件
        if hasattr(self, 'target_type_combo') and self.target_type_combo.currentText() != "全部":
            conditions.append("target_type = ?")
            params.append(self.target_type_combo.currentText())

        # 是否脱贫户条件
        if hasattr(self, 'hh_type_combo') and self.hh_type_combo.currentText() != "全部":
            if self.hh_type_combo.currentText() == "是":
                conditions.append("hh_type = ?")
                params.append("是")
            else:  # "否"
                conditions.append("hh_type = ?")
                params.append("否")

        # 查询区域条件 - 多级联动
        if hasattr(self, 'prov_combo') and self.prov_combo.currentText() != "全部省份":
            conditions.append("prov = ?")
            params.append(self.prov_combo.currentText())

        if hasattr(self, 'city_combo') and self.city_combo.currentText() != "全部市":
            conditions.append("city = ?")
            params.append(self.city_combo.currentText())

        if hasattr(self, 'county_combo') and self.county_combo.currentText() != "全部县":
            conditions.append("county = ?")
            params.append(self.county_combo.currentText())

        if hasattr(self, 'town_combo') and self.town_combo.currentText() != "全部镇":
            conditions.append("town = ?")
            params.append(self.town_combo.currentText())

        if hasattr(self, 'vill_combo') and self.vill_combo.currentText() != "全部村":
            conditions.append("vill = ?")
            params.append(self.vill_combo.currentText())

        return conditions, params

    def export_data(self):
        """导出所有查询结果数据"""
        try:
            # 检查是否有总记录数，如果没有说明还没有进行过查询
            if not hasattr(self, 'total_records') or self.total_records == 0:
                QMessageBox.warning(None, "警告", "没有可导出的数据，请先进行查询")
                return

            from PyQt5.QtWidgets import QFileDialog, QProgressDialog, QApplication
            import duckdb

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                None,
                "导出户信息数据",
                "PLCFHR_HouseholdInfo.csv",
                "CSV文件 (*.csv);;Excel文件 (*.xlsx);;所有文件 (*.*)"
            )

            if not file_path:
                return

            # 显示进度对话框
            progress = QProgressDialog("正在导出数据...", "取消", 0, 100, None)
            progress.setWindowTitle("导出进度")
            progress.setModal(True)
            progress.show()
            QApplication.processEvents()

            # 连接数据库
            conn = duckdb.connect('shuju.duckdb')

            # 获取当前查询条件
            conditions, params = self.get_current_query_conditions()

            # 构建完整查询（不分页）
            base_query = "SELECT * FROM PLCFHR_HouseholdInfo"
            where_clause = f" WHERE {' AND '.join(conditions)}" if conditions else ""
            full_query = f"{base_query}{where_clause} ORDER BY hh_id"

            progress.setValue(30)
            QApplication.processEvents()

            # 执行查询获取所有数据
            if params:
                all_data = conn.execute(full_query, params).fetchdf()
            else:
                all_data = conn.execute(full_query).fetchdf()

            progress.setValue(70)
            QApplication.processEvents()

            # 根据文件扩展名选择导出格式
            if file_path.endswith('.xlsx'):
                all_data.to_excel(file_path, index=False)
            else:
                all_data.to_csv(file_path, index=False, encoding='utf-8-sig')

            progress.setValue(100)
            QApplication.processEvents()

            conn.close()
            progress.close()

            QMessageBox.information(None, "成功", f"已成功导出 {len(all_data):,} 条记录到: {file_path}")

        except Exception as e:
            if 'progress' in locals():
                progress.close()
            QMessageBox.warning(None, "错误", f"导出数据时出错: {str(e)}")

    def init_region_selectors(self):
        """初始化区域选择器"""
        try:
            # 加载省级数据
            self.load_provinces()

            # 绑定联动事件
            self.prov_combo.currentTextChanged.connect(self.on_province_changed)
            self.city_combo.currentTextChanged.connect(self.on_city_changed)
            self.county_combo.currentTextChanged.connect(self.on_county_changed)
            self.town_combo.currentTextChanged.connect(self.on_town_changed)

        except Exception as e:
            print(f"初始化区域选择器时出错: {e}")

    def load_provinces(self):
        """加载省级数据"""
        try:
            import duckdb
            conn = duckdb.connect('shuju.duckdb')

            # 查询所有省份
            query = "SELECT DISTINCT prov FROM PLCFHR_HouseholdInfo WHERE prov IS NOT NULL ORDER BY prov"
            result = conn.execute(query).fetchall()

            provinces = ["全部省份"] + [row[0] for row in result if row[0]]

            self.prov_combo.clear()
            self.prov_combo.addItems(provinces)

            conn.close()

        except Exception as e:
            print(f"加载省份数据时出错: {e}")
            self.prov_combo.clear()
            self.prov_combo.addItems(["全部省份"])

    def on_province_changed(self, province):
        """省份变更时的处理"""
        try:
            # 清空下级选择器
            self.city_combo.clear()
            self.county_combo.clear()
            self.town_combo.clear()
            self.vill_combo.clear()

            if province == "全部省份":
                self.city_combo.setEnabled(False)
                self.county_combo.setEnabled(False)
                self.town_combo.setEnabled(False)
                self.vill_combo.setEnabled(False)
                return

            # 加载对应的市级数据
            self.load_cities(province)
            self.city_combo.setEnabled(True)

        except Exception as e:
            print(f"处理省份变更时出错: {e}")

    def load_cities(self, province):
        """加载市级数据"""
        try:
            import duckdb
            conn = duckdb.connect('shuju.duckdb')

            query = "SELECT DISTINCT city FROM PLCFHR_HouseholdInfo WHERE prov = ? AND city IS NOT NULL ORDER BY city"
            result = conn.execute(query, [province]).fetchall()

            cities = ["全部市"] + [row[0] for row in result if row[0]]

            self.city_combo.clear()
            self.city_combo.addItems(cities)

            conn.close()

        except Exception as e:
            print(f"加载市级数据时出错: {e}")
            self.city_combo.clear()
            self.city_combo.addItems(["全部市"])

    def on_city_changed(self, city):
        """市变更时的处理"""
        try:
            # 清空下级选择器
            self.county_combo.clear()
            self.town_combo.clear()
            self.vill_combo.clear()

            if city == "全部市":
                self.county_combo.setEnabled(False)
                self.town_combo.setEnabled(False)
                self.vill_combo.setEnabled(False)
                return

            # 加载对应的县级数据
            province = self.prov_combo.currentText()
            self.load_counties(province, city)
            self.county_combo.setEnabled(True)

        except Exception as e:
            print(f"处理市变更时出错: {e}")

    def load_counties(self, province, city):
        """加载县级数据"""
        try:
            import duckdb
            conn = duckdb.connect('shuju.duckdb')

            query = "SELECT DISTINCT county FROM PLCFHR_HouseholdInfo WHERE prov = ? AND city = ? AND county IS NOT NULL ORDER BY county"
            result = conn.execute(query, [province, city]).fetchall()

            counties = ["全部县"] + [row[0] for row in result if row[0]]

            self.county_combo.clear()
            self.county_combo.addItems(counties)

            conn.close()

        except Exception as e:
            print(f"加载县级数据时出错: {e}")
            self.county_combo.clear()
            self.county_combo.addItems(["全部县"])

    def on_county_changed(self, county):
        """县变更时的处理"""
        try:
            # 清空下级选择器
            self.town_combo.clear()
            self.vill_combo.clear()

            if county == "全部县":
                self.town_combo.setEnabled(False)
                self.vill_combo.setEnabled(False)
                return

            # 加载对应的镇级数据
            province = self.prov_combo.currentText()
            city = self.city_combo.currentText()
            self.load_towns(province, city, county)
            self.town_combo.setEnabled(True)

        except Exception as e:
            print(f"处理县变更时出错: {e}")

    def load_towns(self, province, city, county):
        """加载镇级数据"""
        try:
            import duckdb
            conn = duckdb.connect('shuju.duckdb')

            query = "SELECT DISTINCT town FROM PLCFHR_HouseholdInfo WHERE prov = ? AND city = ? AND county = ? AND town IS NOT NULL ORDER BY town"
            result = conn.execute(query, [province, city, county]).fetchall()

            towns = ["全部镇"] + [row[0] for row in result if row[0]]

            self.town_combo.clear()
            self.town_combo.addItems(towns)

            conn.close()

        except Exception as e:
            print(f"加载镇级数据时出错: {e}")
            self.town_combo.clear()
            self.town_combo.addItems(["全部镇"])

    def on_town_changed(self, town):
        """镇变更时的处理"""
        try:
            # 清空下级选择器
            self.vill_combo.clear()

            if town == "全部镇":
                self.vill_combo.setEnabled(False)
                return

            # 加载对应的村级数据
            province = self.prov_combo.currentText()
            city = self.city_combo.currentText()
            county = self.county_combo.currentText()
            self.load_villages(province, city, county, town)
            self.vill_combo.setEnabled(True)

        except Exception as e:
            print(f"处理镇变更时出错: {e}")

    def load_villages(self, province, city, county, town):
        """加载村级数据"""
        try:
            import duckdb
            conn = duckdb.connect('shuju.duckdb')

            query = "SELECT DISTINCT vill FROM PLCFHR_HouseholdInfo WHERE prov = ? AND city = ? AND county = ? AND town = ? AND vill IS NOT NULL ORDER BY vill"
            result = conn.execute(query, [province, city, county, town]).fetchall()

            villages = ["全部村"] + [row[0] for row in result if row[0]]

            self.vill_combo.clear()
            self.vill_combo.addItems(villages)

            conn.close()

        except Exception as e:
            print(f"加载村级数据时出错: {e}")
            self.vill_combo.clear()
            self.vill_combo.addItems(["全部村"])


def create_basic_info_page(parent=None):
    """创建基础信息页面的工厂函数

    Args:
        parent: 父窗口引用

    Returns:
        QWidget: 基础信息页面小部件
    """
    household_info = HouseholdInfoPage(parent)
    return household_info.create_page()